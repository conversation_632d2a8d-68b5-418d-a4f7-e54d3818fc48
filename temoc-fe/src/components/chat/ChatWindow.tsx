'use client';

import React, { useState, useEffect } from 'react';
import { X, Minimize2, RotateCcw} from 'lucide-react';
import MessageList from './MessageList';
import MessageInput from './MessageInput';
import { ChatMessage, chatService, SendMessageDto } from '@/services/chat.service';
import { useAuth } from '@/hooks/useAuth';
import { usePathname } from 'next/navigation';
import { toast } from 'react-toastify';

interface ChatWindowProps {
  isOpen: boolean;
  onClose: () => void;
  onMinimize?: () => void;
}

const ChatWindow: React.FC<ChatWindowProps> = ({
  isOpen,
  onClose,
  onMinimize,
}) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  
  const { user } = useAuth();
  const pathname = usePathname();

  // Get current page context
  const getCurrentPageContext = () => {
    if (pathname.includes('/profile')) return 'profile';
    if (pathname.includes('/explore')) return 'explore';
    if (pathname.includes('/discovery')) return 'discovery';
    if (pathname.includes('/library')) return 'library';
    if (pathname.includes('/trading')) return 'trading';
    if (pathname.includes('/help')) return 'help';
    return 'unknown';
  };

  // Get user role
  const getUserRole = () => {
    return user?.role || 'fan';
  };

  const handleSendMessage = async (message: string) => {
    if (!user || isLoading) return;

    setIsLoading(true);
    setIsTyping(true);

    // Add user message to UI immediately
    const userMessage: ChatMessage = {
      _id: `temp-${Date.now()}`,
      sessionId: sessionId || '',
      userId: user._id,
      message,
      role: 'user',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    setMessages(prev => [...prev, userMessage]);

    try {
      const sendData: SendMessageDto = {
        message,
        sessionId: sessionId || undefined,
        currentPage: getCurrentPageContext(),
        userRole: getUserRole(),
        metadata: {
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
        },
      };

      const response = await chatService.sendMessage(sendData);
      console.log('Chat response:', response);

      if (response.success && response.data) {
        // Update session ID if it's a new session
        if (!sessionId) {
          setSessionId(response.data.sessionId);
        }

        // Replace temp user message and add AI response
        setMessages(prev => {
          const filtered = prev.filter(msg => msg._id !== userMessage._id);
          const aiMessage: ChatMessage = {
            _id: `ai-${Date.now()}`,
            sessionId: response.data.sessionId,
            userId: user._id,
            message: response.data.message,
            role: 'assistant',
            response: response.data.response,
            responseTime: response.data.responseTime,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };

          return [...filtered, {
            ...userMessage,
            sessionId: response.data.sessionId,
          }, aiMessage];
        });
      } else {
        console.error('API Response:', response);
        toast.error('Failed to send message. Please try again.');
        // Remove the temp message on error
        setMessages(prev => prev.filter(msg => msg._id !== userMessage._id));
      }
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Network error. Please check your connection and try again.');
      // Remove the temp message on error
      setMessages(prev => prev.filter(msg => msg._id !== userMessage._id));
    } finally {
      setIsLoading(false);
      setIsTyping(false);
    }
  };

  const handleFeedback = async (messageId: string, feedback: 'helpful' | 'not_helpful') => {
    try {
      await chatService.provideFeedback({ messageId, feedback });
      toast.success('Thank you for your feedback!');
    } catch (error) {
      console.error('Error providing feedback:', error);
      toast.error('Failed to submit feedback.');
    }
  };

  const handleNewChat = () => {
    setMessages([]);
    setSessionId(null);
    setIsTyping(false);
  };

  const handleEndSession = async () => {
    if (sessionId) {
      try {
        await chatService.endSession(sessionId);
      } catch (error) {
        console.error('Error ending session:', error);
      }
    }
    handleNewChat();
    onClose();
  };

  // Load chat history when session ID changes
  useEffect(() => {
    if (sessionId && isOpen) {
      const loadHistory = async () => {
        try {
          const response = await chatService.getChatHistory(sessionId);
          if (response.success) {
            setMessages(response.data);
          }
        } catch (error) {
          console.error('Error loading chat history:', error);
        }
      };
      loadHistory();
    }
  }, [sessionId, isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed bottom-20 right-6 z-50 w-[420px] h-[600px] bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 rounded-2xl shadow-2xl border border-gray-700 flex flex-col backdrop-blur-sm">
      {/* Header */}
      <div className="flex items-center justify-between p-5 border-b border-gray-600 bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800 rounded-t-2xl relative overflow-hidden">
        {/* Subtle orange accent line */}
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-orange-500 to-transparent"></div>

        <div className="flex items-center space-x-3">
          <div className="relative">
            <div className="h-12 w-12 rounded-full bg-gradient-to-br from-orange-600 to-orange-700 flex items-center justify-center shadow-xl border-2 border-gray-600">
              <span className="text-xl font-bold text-white">T</span>
            </div>
            <div className="absolute -bottom-1 -right-1 h-4 w-4 bg-green-500 rounded-full border-2 border-gray-800 shadow-lg animate-pulse"></div>
          </div>
          <div>
            <h3 className="font-bold text-white text-lg tracking-wide">TEMOC Assistant</h3>
            <p className="text-sm text-gray-300 flex items-center">
              <div className="w-2 h-2 bg-orange-500 rounded-full mr-2 animate-pulse shadow-lg"></div>
              {isTyping ? (
                <span className="text-orange-400">AI is thinking...</span>
              ) : (
                <span>Ready to help you</span>
              )}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={handleNewChat}
            className="p-2 text-gray-300 hover:text-[#FACA00] hover:bg-gray-600/50 rounded-lg transition-all duration-200 backdrop-blur-sm"
            title="New chat"
          >
            <RotateCcw className="h-5 w-5" />
          </button>
          {onMinimize && (
            <button
              onClick={onMinimize}
              className="p-2 text-gray-300 hover:text-[#FACA00] hover:bg-gray-600/50 rounded-lg transition-all duration-200 backdrop-blur-sm"
              title="Minimize"
            >
              <Minimize2 className="h-5 w-5" />
            </button>
          )}
          <button
            onClick={handleEndSession}
            className="p-2 text-gray-300 hover:text-red-400 hover:bg-gray-600/50 rounded-lg transition-all duration-200 backdrop-blur-sm"
            title="Close chat"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* Messages */}
      <MessageList
        messages={messages}
        isTyping={isTyping}
        onFeedback={handleFeedback}
      />

      {/* Input */}
      <MessageInput
        onSendMessage={handleSendMessage}
        disabled={isLoading || !user}
        placeholder={user ? "Ask me anything about TEMOC..." : "Please log in to chat"}
      />

      {/* Quick help buttons */}
      <div className="p-4 border-t border-gray-600 bg-gradient-to-t from-gray-900 to-gray-800">
        <div className="flex flex-wrap gap-2">
          {[
            "How to create a token?",
            "How to upload music?",
            "What is liquidity?",
            "How to buy tokens?",
          ].map((question) => (
            <button
              key={question}
              onClick={() => handleSendMessage(question)}
              disabled={isLoading || !user}
              className="text-xs px-4 py-2 bg-gradient-to-r from-gray-700 to-gray-600 hover:from-orange-600/20 hover:to-orange-500/20 text-gray-300 hover:text-orange-400 rounded-full transition-all duration-200 border border-gray-600 hover:border-orange-500/50 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:from-gray-700 disabled:hover:to-gray-600 disabled:hover:text-gray-300"
            >
              {question}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ChatWindow;
