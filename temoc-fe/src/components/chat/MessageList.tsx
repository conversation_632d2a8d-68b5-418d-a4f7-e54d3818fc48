'use client';

import React, { useEffect, useRef } from 'react';
import { ThumbsUp, ThumbsDown, Copy, User } from 'lucide-react';
import { ChatMessage } from '@/services/chat.service';
import TypingIndicator from './TypingIndicator';

interface MessageListProps {
  messages: ChatMessage[];
  isTyping?: boolean;
  onFeedback?: (messageId: string, feedback: 'helpful' | 'not_helpful') => void;
}

const MessageList: React.FC<MessageListProps> = ({
  messages,
  isTyping = false,
  onFeedback,
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, isTyping]);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  return (
    <div className="flex-1 overflow-y-auto p-5 space-y-6 bg-gradient-to-b from-gray-800/50 to-gray-900/50">
      {messages.length === 0 && !isTyping && (
        <div className="flex flex-col items-center justify-center h-full text-center px-6">
          <div className="relative mb-6">
            <div className="h-20 w-20 rounded-full bg-gradient-to-br from-orange-600 to-orange-700 flex items-center justify-center shadow-2xl">
              <span className="text-3xl font-bold text-white">T</span>
            </div>
            <div className="absolute -top-2 -right-2 h-6 w-6 bg-green-500 rounded-full border-4 border-gray-800 animate-pulse"></div>
          </div>
          <h3 className="text-xl font-bold text-white mb-3">
            Welcome to TEMOC Assistant!
          </h3>
          <p className="text-gray-300 max-w-sm leading-relaxed text-sm">
            I'm your AI guide for the TEMOC platform. Ask me about
            <span className="text-orange-400 font-semibold"> token creation</span>,
            <span className="text-orange-400 font-semibold"> music uploads</span>,
            <span className="text-orange-400 font-semibold"> trading</span>, or anything else!
          </p>
          <div className="mt-4 flex space-x-2">
            <div className="w-2 h-2 bg-orange-500 rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-orange-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-2 h-2 bg-orange-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
        </div>
      )}

      {messages.map((message) => (
        <div key={message._id} className="space-y-4">
          {/* User Message */}
          <div className="flex justify-end">
            <div className="flex items-end space-x-3 max-w-[85%]">
              <div className="bg-gradient-to-br from-blue-600 to-blue-700 text-white rounded-2xl rounded-br-md px-5 py-3 shadow-lg border border-blue-500/30">
                <p className="text-sm font-medium whitespace-pre-wrap leading-relaxed">{message.message}</p>
                <div className="text-xs opacity-90 mt-2 text-right">
                  {formatTime(message.createdAt)}
                </div>
              </div>
              <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center shadow-lg border-2 border-gray-600">
                <User className="h-5 w-5 text-white" />
              </div>
            </div>
          </div>

          {/* AI Response */}
          {message.response && (
            <div className="flex justify-start">
              <div className="flex items-start space-x-3 max-w-[85%]">
                <div className="h-10 w-10 rounded-full bg-gradient-to-br from-[#FACA00] to-[#FFD700] flex items-center justify-center flex-shrink-0 shadow-lg">
                  <span className="text-sm font-bold text-white">T</span>
                </div>
                <div className="bg-gradient-to-br from-gray-700 to-gray-800 rounded-2xl rounded-bl-md px-5 py-4 shadow-lg border border-gray-600">
                  <p className="text-sm text-gray-100 whitespace-pre-wrap leading-relaxed font-medium">
                    {message.response}
                  </p>
                  <div className="flex items-center justify-between mt-3 pt-2 border-t border-gray-600">
                    <div className="text-xs text-gray-400 flex items-center">
                      <div className="w-1.5 h-1.5 bg-green-400 rounded-full mr-2"></div>
                      {formatTime(message.updatedAt)}
                      {message.responseTime && (
                        <span className="ml-2 px-2 py-1 bg-gray-600 rounded-full text-xs">
                          {message.responseTime}ms
                        </span>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => copyToClipboard(message.response!)}
                        className="p-2 text-gray-400 hover:text-[#FACA00] hover:bg-gray-600 rounded-lg transition-all duration-200"
                        title="Copy response"
                      >
                        <Copy className="h-4 w-4" />
                      </button>
                      {onFeedback && (
                        <>
                          <button
                            onClick={() => onFeedback(message._id, 'helpful')}
                            className="p-2 text-gray-400 hover:text-green-400 hover:bg-gray-600 rounded-lg transition-all duration-200"
                            title="Helpful"
                          >
                            <ThumbsUp className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => onFeedback(message._id, 'not_helpful')}
                            className="p-2 text-gray-400 hover:text-red-400 hover:bg-gray-600 rounded-lg transition-all duration-200"
                            title="Not helpful"
                          >
                            <ThumbsDown className="h-4 w-4" />
                          </button>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      ))}

      {isTyping && <TypingIndicator />}
      
      <div ref={messagesEndRef} />
    </div>
  );
};

export default MessageList;
