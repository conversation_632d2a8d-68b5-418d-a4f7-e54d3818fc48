'use client';

import React from 'react';
import { MessageCircle, X } from 'lucide-react';

interface ChatBubbleProps {
  isOpen: boolean;
  onClick: () => void;
  hasUnreadMessages?: boolean;
}

const ChatBubble: React.FC<ChatBubbleProps> = ({
  isOpen,
  onClick,
  hasUnreadMessages = false,
}) => {
  return (
    <div className="fixed bottom-6 right-6 z-50">
      <button
        onClick={onClick}
        className={`
          relative flex h-14 w-14 items-center justify-center rounded-full
          shadow-lg transition-all duration-300 ease-in-out hover:scale-110 group
          ${isOpen
            ? 'bg-gray-600 hover:bg-gray-700'
            : 'bg-[#FACA00] hover:bg-[#e6b600]'
          }
        `}
        aria-label={isOpen ? 'Close chat' : 'Open chat'}
      >
        {/* Unread message indicator */}
        {hasUnreadMessages && !isOpen && (
          <div className="absolute -top-1 -right-1 h-4 w-4 rounded-full bg-red-500 border-2 border-white">
            <div className="h-full w-full rounded-full bg-red-500 animate-pulse" />
          </div>
        )}

        {/* Icon */}
        <div className="transition-transform duration-300">
          {isOpen ? (
            <X className="h-6 w-6 text-white" />
          ) : (
            <MessageCircle className="h-6 w-6 text-white" />
          )}
        </div>
      </button>

      {/* Tooltip */}
      {!isOpen && (
        <div className="absolute bottom-16 right-0 mb-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
          <div className="bg-gray-800 text-white text-sm px-3 py-2 rounded-lg whitespace-nowrap">
            Need help? Chat with TEMOC Assistant
            <div className="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800" />
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatBubble;
