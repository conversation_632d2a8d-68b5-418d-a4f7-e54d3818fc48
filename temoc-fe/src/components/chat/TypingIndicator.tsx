'use client';

import React from 'react';

const TypingIndicator: React.FC = () => {
  return (
    <div className="flex items-center space-x-2 p-3">
      <div className="flex items-center space-x-1">
        <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
          <div className="h-6 w-6 rounded-full bg-[#FACA00] flex items-center justify-center">
            <span className="text-xs font-bold text-white">T</span>
          </div>
        </div>
        <div className="bg-gray-100 rounded-2xl px-4 py-2">
          <div className="flex space-x-1">
            <div className="h-2 w-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
            <div className="h-2 w-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
            <div className="h-2 w-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default TypingIndicator;
