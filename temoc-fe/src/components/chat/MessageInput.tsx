'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Send, Paperclip } from 'lucide-react';

interface MessageInputProps {
  onSendMessage: (message: string) => void;
  disabled?: boolean;
  placeholder?: string;
}

const MessageInput: React.FC<MessageInputProps> = ({
  onSendMessage,
  disabled = false,
  placeholder = "Type your message...",
}) => {
  const [message, setMessage] = useState('');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim() && !disabled) {
      onSendMessage(message.trim());
      setMessage('');
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value);
    
    // Auto-resize textarea
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`;
    }
  };

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  }, []);

  return (
    <div className="border-t border-gray-600 bg-gradient-to-t from-gray-900 to-gray-800 p-4">
      <form onSubmit={handleSubmit} className="flex items-end space-x-3">
        <div className="flex-1 relative">
          <textarea
            ref={textareaRef}
            value={message}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            placeholder={placeholder}
            disabled={disabled}
            rows={1}
            className={`
              w-full resize-none rounded-xl border border-gray-600 px-4 py-3 pr-12
              bg-gray-700 text-gray-100 placeholder-gray-400
              focus:border-[#FACA00] focus:outline-none focus:ring-2 focus:ring-[#FACA00]/30
              disabled:bg-gray-800 disabled:cursor-not-allowed disabled:text-gray-500
              max-h-[120px] overflow-y-auto shadow-lg
              transition-all duration-200
            `}
            style={{ minHeight: '44px' }}
          />

          {/* Attachment button (for future use) */}
          <button
            type="button"
            className="absolute right-3 top-1/2 -translate-y-1/2 p-1.5 text-gray-400 hover:text-[#FACA00] hover:bg-gray-600 rounded-lg transition-all duration-200"
            disabled={disabled}
          >
            <Paperclip className="h-4 w-4" />
          </button>
        </div>

        <button
          type="submit"
          disabled={disabled || !message.trim()}
          className={`
            flex h-12 w-12 items-center justify-center rounded-xl transition-all duration-200 shadow-lg
            ${disabled || !message.trim()
              ? 'bg-gray-600 cursor-not-allowed border border-gray-600'
              : 'bg-gradient-to-br from-[#FACA00] to-[#FFD700] hover:from-[#e6b600] hover:to-[#e6c200] hover:scale-105 hover:shadow-xl border border-[#FACA00]'
            }
          `}
        >
          <Send className={`h-5 w-5 ${disabled || !message.trim() ? 'text-gray-400' : 'text-white'}`} />
        </button>
      </form>

      <div className="mt-3 text-xs text-gray-400 flex items-center">
        <div className="w-1.5 h-1.5 bg-[#FACA00] rounded-full mr-2"></div>
        Press Enter to send, Shift + Enter for new line
      </div>
    </div>
  );
};

export default MessageInput;
