'use client';

import React, { useState } from 'react';
import ChatBubble from './ChatBubble';
import ChatWindow from './ChatWindow';

const ChatBot: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);

  const handleToggleChat = () => {
    if (isMinimized) {
      setIsMinimized(false);
    } else {
      setIsOpen(!isOpen);
    }
  };

  const handleCloseChat = () => {
    setIsOpen(false);
    setIsMinimized(false);
  };

  const handleMinimizeChat = () => {
    setIsMinimized(true);
  };

  return (
    <>
      <ChatBubble
        isOpen={isOpen && !isMinimized}
        onClick={handleToggleChat}
        hasUnreadMessages={false} // You can implement this logic later
      />
      
      <ChatWindow
        isOpen={isOpen && !isMinimized}
        onClose={handleCloseChat}
        onMinimize={handleMinimizeChat}
      />
    </>
  );
};

export default ChatBot;
