import { HttpService } from './base.service';

export interface SendMessageDto {
  message: string;
  sessionId?: string;
  currentPage?: string;
  userRole?: string;
  metadata?: Record<string, any>;
}

export interface ChatResponseDto {
  sessionId: string;
  message: string;
  response: string;
  responseTime: number;
  timestamp: Date;
}

export interface ChatMessage {
  _id: string;
  sessionId: string;
  userId: string;
  message: string;
  role: 'user' | 'assistant';
  response?: string;
  responseTime?: number;
  createdAt: string;
  updatedAt: string;
}

export interface FeedbackDto {
  messageId: string;
  feedback: 'helpful' | 'not_helpful';
}

class ChatService extends HttpService {
  private readonly prefix: string = 'chat';

  /**
   * Send a message to the AI chatbot
   */
  sendMessage = async (data: SendMessageDto): Promise<{ success: boolean; data: ChatResponseDto }> => {
    const response = await this.post(`${this.prefix}/message`, data);
    return response.data;
  };

  /**
   * Get chat history for a session
   */
  getChatHistory = async (sessionId: string): Promise<{ success: boolean; data: ChatMessage[] }> => {
    const response = await this.get(`${this.prefix}/history/${sessionId}`);
    return response.data;
  };

  /**
   * Provide feedback on a message
   */
  provideFeedback = async (data: FeedbackDto): Promise<{ success: boolean; message: string }> => {
    const response = await this.patch(`${this.prefix}/feedback`, data);
    return response.data;
  };

  /**
   * End a chat session
   */
  endSession = async (sessionId: string): Promise<{ success: boolean; message: string }> => {
    const response = await this.post(`${this.prefix}/end-session/${sessionId}`, {});
    return response.data;
  };
}

export const chatService = new ChatService();
