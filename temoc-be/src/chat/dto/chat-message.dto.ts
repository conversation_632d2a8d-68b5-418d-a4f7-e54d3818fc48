import { IsString, <PERSON>NotEmpty, <PERSON>Optional, IsEnum } from 'class-validator';

export class SendMessageDto {
  @IsString()
  @IsNotEmpty()
  message: string;

  @IsOptional()
  @IsString()
  sessionId?: string;

  @IsOptional()
  @IsString()
  currentPage?: string;

  @IsOptional()
  @IsEnum(['fan', 'artist', 'admin'])
  userRole?: string;

  @IsOptional()
  metadata?: Record<string, any>;
}

export class ChatResponseDto {
  sessionId: string;
  message: string;
  response: string;
  responseTime: number;
  timestamp: Date;
}

export class FeedbackDto {
  @IsString()
  @IsNotEmpty()
  messageId: string;

  @IsString()
  @IsEnum(['helpful', 'not_helpful'])
  feedback: string;
}
