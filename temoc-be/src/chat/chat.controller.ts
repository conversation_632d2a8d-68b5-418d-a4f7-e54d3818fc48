import {
  <PERSON>,
  Post,
  Get,
  Body,
  Param,
  UseGuards,
  Query,
  Patch,
} from '@nestjs/common';
import { ChatService } from './chat.service';
import { SendMessageDto, FeedbackDto } from './dto/chat-message.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AuthUser } from '../auth/decorators/user.decorator';

@Controller('chat')
@UseGuards(JwtAuthGuard)
export class ChatController {
  constructor(private readonly chatService: ChatService) {}

  @Post('message')
  async sendMessage(
    @AuthUser() user: any,
    @Body() sendMessageDto: SendMessageDto,
  ) {
    try {
      const response = await this.chatService.sendMessage(
        user._id,
        sendMessageDto,
      );
      
      return {
        success: true,
        data: response,
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
      };
    }
  }

  @Get('history/:sessionId')
  async getChatHistory(
    @AuthUser() user: any,
    @Param('sessionId') sessionId: string,
  ) {
    try {
      const history = await this.chatService.getChatHistory(user._id, sessionId);
      
      return {
        success: true,
        data: history,
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
      };
    }
  }

  @Patch('feedback')
  async provideFeedback(
    @Body() feedbackDto: FeedbackDto,
  ) {
    try {
      await this.chatService.markMessageHelpful(
        feedbackDto.messageId,
        feedbackDto.feedback === 'helpful',
      );
      
      return {
        success: true,
        message: 'Feedback recorded successfully',
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
      };
    }
  }

  @Post('end-session/:sessionId')
  async endSession(
    @AuthUser() user: any,
    @Param('sessionId') sessionId: string,
  ) {
    try {
      await this.chatService.endSession(user._id, sessionId);
      
      return {
        success: true,
        message: 'Chat session ended successfully',
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
      };
    }
  }
}
