import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { ConfigService } from '@nestjs/config';
import { GoogleGenAI } from '@google/genai';
import { ChatSession, ChatSessionDocument } from './schemas/chat-session.schema';
import { ChatMessage, ChatMessageDocument } from './schemas/chat-message.schema';
import { SendMessageDto, ChatResponseDto } from './dto/chat-message.dto';
import { TEMOC_KNOWLEDGE_BASE, getContextualHelp } from './knowledge-base';

@Injectable()
export class ChatService {
  private readonly logger = new Logger(ChatService.name);
  private genAI: GoogleGenAI;

  constructor(
    @InjectModel(ChatSession.name)
    private chatSessionModel: Model<ChatSessionDocument>,
    @InjectModel(ChatMessage.name)
    private chatMessageModel: Model<ChatMessageDocument>,
    private configService: ConfigService,
  ) {
    const apiKey = this.configService.get<string>('GEMINI_API_KEY');
    if (!apiKey) {
      this.logger.error('GEMINI_API_KEY not found in environment variables');
      throw new Error('GEMINI_API_KEY is required');
    }
    this.genAI = new GoogleGenAI({ apiKey });
  }

  async sendMessage(
    userId: string,
    sendMessageDto: SendMessageDto,
  ): Promise<ChatResponseDto> {
    const startTime = Date.now();
    
    try {
      // Get or create chat session
      let session = await this.getOrCreateSession(
        userId,
        sendMessageDto.sessionId,
        sendMessageDto.userRole,
        sendMessageDto.currentPage,
      );

      // Get conversation history
      const history = await this.getConversationHistory(session._id);

      // Generate AI response
      const aiResponse = await this.generateAIResponse(
        sendMessageDto.message,
        history,
        sendMessageDto.userRole,
        sendMessageDto.currentPage,
      );

      const responseTime = Date.now() - startTime;

      // Save user message and AI response
      const chatMessage = new this.chatMessageModel({
        sessionId: session._id,
        userId: new Types.ObjectId(userId),
        message: sendMessageDto.message,
        role: 'user',
        response: aiResponse,
        responseTime,
        metadata: sendMessageDto.metadata,
      });

      await chatMessage.save();

      // Update session last activity
      session.lastActivity = new Date();
      await session.save();

      return {
        sessionId: session.sessionId,
        message: sendMessageDto.message,
        response: aiResponse,
        responseTime,
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error(`Error processing chat message: ${error.message}`, error.stack);
      throw new Error('Failed to process chat message');
    }
  }

  private async getOrCreateSession(
    userId: string,
    sessionId?: string,
    userRole?: string,
    currentPage?: string,
  ): Promise<ChatSessionDocument> {
    if (sessionId) {
      const existingSession = await this.chatSessionModel.findOne({
        sessionId,
        userId: new Types.ObjectId(userId),
        isActive: true,
      });
      
      if (existingSession) {
        // Update context if provided
        if (userRole) existingSession.userRole = userRole;
        if (currentPage) existingSession.currentPage = currentPage;
        await existingSession.save();
        return existingSession;
      }
    }

    // Create new session
    const newSessionId = `chat_${userId}_${Date.now()}`;
    const newSession = new this.chatSessionModel({
      userId: new Types.ObjectId(userId),
      sessionId: newSessionId,
      userRole: userRole || 'fan',
      currentPage: currentPage || 'unknown',
      isActive: true,
    });

    return await newSession.save();
  }

  private async getConversationHistory(sessionId: Types.ObjectId): Promise<any[]> {
    const messages = await this.chatMessageModel
      .find({ sessionId })
      .sort({ createdAt: 1 })
      .limit(20) // Limit to last 20 messages to manage token usage
      .exec();

    const history = [];
    for (const msg of messages) {
      history.push({
        role: 'user',
        parts: [{ text: msg.message }],
      });
      if (msg.response) {
        history.push({
          role: 'model',
          parts: [{ text: msg.response }],
        });
      }
    }

    return history;
  }

  private async generateAIResponse(
    message: string,
    history: any[],
    userRole?: string,
    currentPage?: string,
  ): Promise<string> {
    try {
      const systemInstruction = this.buildSystemInstruction(userRole, currentPage);
      
      const chat = this.genAI.chats.create({
        model: 'gemini-2.0-flash',
        history,
        config: {
          systemInstruction,
          maxOutputTokens: 500,
          temperature: 0.7,
        },
      });

      const response = await chat.sendMessage({ message });
      return response.text || 'I apologize, but I couldn\'t generate a response. Please try again.';
    } catch (error) {
      this.logger.error(`Error generating AI response: ${error.message}`, error.stack);
      return 'I\'m experiencing some technical difficulties. Please try again in a moment.';
    }
  }

  private buildSystemInstruction(userRole?: string, currentPage?: string): string {
    const kb = TEMOC_KNOWLEDGE_BASE;
    const contextualHelp = getContextualHelp(userRole || 'fan', currentPage || 'unknown');

    const baseInstruction = `You are TEMOC Assistant, a helpful AI chatbot for the TEMOC platform.

PLATFORM OVERVIEW:
${kb.platform.description} ${kb.platform.tagline}

KEY FEATURES:
${kb.keyFeatures.map(feature => `- ${feature.name}: ${feature.description}`).join('\n')}

BLOCKCHAIN DETAILS:
- Network: ${kb.blockchain.network}
- Wallet: ${kb.blockchain.wallet}
- DEX: ${kb.blockchain.dex}
- Gas Token: ${kb.blockchain.gasToken}

CONTENT HIERARCHY:
${kb.contentManagement.hierarchy}
- Libraries: ${kb.contentManagement.libraries.description}
- Albums: ${kb.contentManagement.albums.description}
- Tracks: ${kb.contentManagement.tracks.description}

TOKEN CREATION PROCESS:
${kb.tokenCreation.process.map((step, i) => `${i + 1}. ${step}`).join('\n')}

TRADING INFORMATION:
- Platform: ${kb.trading.platform}
- Liquidity: ${kb.trading.liquidity.description}
- Importance: ${kb.trading.liquidity.importance}

RESPONSE GUIDELINES:
- Be helpful, friendly, and professional
- Provide step-by-step instructions when needed
- Use simple language to explain blockchain concepts
- Always encourage users to explore the platform
- If you don't know something, admit it and suggest contacting <NAME_EMAIL>
- Keep responses concise but informative
- Use the knowledge base to provide accurate information
- Suggest relevant quick actions when appropriate`;

    let roleSpecificInstruction = '';
    if (userRole && contextualHelp.role) {
      const role = contextualHelp.role;
      roleSpecificInstruction = `\n\nUSER CONTEXT: You are helping a ${userRole.toUpperCase()}.
Description: ${role.description}
Capabilities: ${role.capabilities.join(', ')}

Focus on helping with: ${role.capabilities.slice(0, 3).join(', ')}`;
    }

    let pageSpecificInstruction = '';
    if (currentPage && contextualHelp.page) {
      pageSpecificInstruction = `\n\nCURRENT PAGE CONTEXT: User is on "${currentPage}" page.
Relevant help: ${contextualHelp.page.join(', ')}`;
    }

    let quickActionsInstruction = '';
    if (contextualHelp.quickActions) {
      quickActionsInstruction = `\n\nSUGGESTED QUICK ACTIONS: ${contextualHelp.quickActions.join(', ')}`;
    }

    // Add relevant FAQ based on user role
    let faqInstruction = '';
    if (userRole === 'artist' && kb.commonQuestions.artists) {
      faqInstruction = `\n\nCOMMON ARTIST QUESTIONS:
${kb.commonQuestions.artists.slice(0, 3).map(qa => `Q: ${qa.q}\nA: ${qa.a}`).join('\n\n')}`;
    } else if (userRole === 'fan' && kb.commonQuestions.fans) {
      faqInstruction = `\n\nCOMMON FAN QUESTIONS:
${kb.commonQuestions.fans.slice(0, 3).map(qa => `Q: ${qa.q}\nA: ${qa.a}`).join('\n\n')}`;
    }

    return baseInstruction + roleSpecificInstruction + pageSpecificInstruction + quickActionsInstruction + faqInstruction;
  }

  async getChatHistory(userId: string, sessionId: string): Promise<ChatMessage[]> {
    const session = await this.chatSessionModel.findOne({
      sessionId,
      userId: new Types.ObjectId(userId),
    });

    if (!session) {
      return [];
    }

    return await this.chatMessageModel
      .find({ sessionId: session._id })
      .sort({ createdAt: 1 })
      .exec();
  }

  async markMessageHelpful(messageId: string, isHelpful: boolean): Promise<void> {
    await this.chatMessageModel.findByIdAndUpdate(messageId, { isHelpful });
  }

  async endSession(userId: string, sessionId: string): Promise<void> {
    await this.chatSessionModel.findOneAndUpdate(
      { sessionId, userId: new Types.ObjectId(userId) },
      { isActive: false },
    );
  }
}
