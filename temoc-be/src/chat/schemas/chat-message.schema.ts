import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type ChatMessageDocument = ChatMessage & Document;

@Schema({ timestamps: true })
export class ChatMessage {
  @Prop({ type: Types.ObjectId, ref: 'ChatSession', required: true })
  sessionId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  userId: Types.ObjectId;

  @Prop({ required: true })
  message: string;

  @Prop({ required: true, enum: ['user', 'assistant'] })
  role: string;

  @Prop()
  response: string; // AI response

  @Prop({ type: Object })
  metadata: Record<string, any>; // Additional data like response time, tokens used, etc.

  @Prop({ default: false })
  isHelpful: boolean; // User feedback

  @Prop()
  responseTime: number; // Time taken to generate response in ms
}

export const ChatMessageSchema = SchemaFactory.createForClass(ChatMessage);
