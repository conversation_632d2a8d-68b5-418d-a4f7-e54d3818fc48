import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type ChatSessionDocument = ChatSession & Document;

@Schema({ timestamps: true })
export class ChatSession {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  userId: Types.ObjectId;

  @Prop({ required: true, unique: true })
  sessionId: string;

  @Prop({ default: true })
  isActive: boolean;

  @Prop()
  userRole: string; // 'fan', 'artist', 'admin'

  @Prop()
  currentPage: string; // Current page context for better responses

  @Prop({ type: Object })
  metadata: Record<string, any>; // Additional context data

  @Prop({ default: Date.now })
  lastActivity: Date;
}

export const ChatSessionSchema = SchemaFactory.createForClass(ChatSession);
