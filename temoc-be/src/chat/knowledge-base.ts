export const TEMOC_KNOWLEDGE_BASE = {
  platform: {
    name: "TEMOC",
    description: "A revolutionary tokenized creator platform that empowers artists to launch their own custom tokens and build exclusive communities for their fans.",
    tagline: "Think of it as a hybrid between Patreon, OnlyFans, and a decentralized exchange - all powered by blockchain technology."
  },

  keyFeatures: [
    {
      name: "Token Launch System",
      description: "Easy-to-use tools for launching artist tokens on multiple blockchains",
      details: "Artists can create custom tokens with their own name, symbol, and total supply. The platform handles all the technical complexity."
    },
    {
      name: "Gated Content",
      description: "Upload exclusive content accessible only to token holders",
      details: "Artists can upload music, videos, behind-the-scenes content, and more that only their token holders can access."
    },
    {
      name: "Revenue Sharing",
      description: "Earn from token transactions, content access, and referrals",
      details: "Artists earn money when fans buy their tokens, access content, and through various revenue streams."
    },
    {
      name: "Community Building",
      description: "Build loyal fanbases through tokenized access and rewards",
      details: "Token holders become part of an exclusive community with special privileges and access."
    },
    {
      name: "Multi-Chain Support",
      description: "Deploy on Ethereum, BSC, Solana, or Base",
      details: "Currently focusing on Base Sepolia testnet for testing, with plans for mainnet deployment."
    },
    {
      name: "Non-Custodial",
      description: "You maintain full control of your tokens and earnings",
      details: "Users connect their own wallets and maintain full control of their assets."
    }
  ],

  userRoles: {
    fan: {
      description: "Music lovers who want to support artists and access exclusive content",
      capabilities: [
        "Discover new artists",
        "Buy artist tokens",
        "Access exclusive content",
        "Follow and interact with artists",
        "Trade tokens on Uniswap",
        "Like and comment on tracks"
      ]
    },
    artist: {
      description: "Musicians and creators who want to monetize their content through tokens",
      capabilities: [
        "Create and launch custom tokens",
        "Upload music content (libraries, albums, tracks)",
        "Set up gated content",
        "Manage artist profile",
        "Add liquidity to Uniswap",
        "Build fan communities"
      ]
    },
    admin: {
      description: "Platform administrators who manage the system",
      capabilities: [
        "Manage user reports",
        "Monitor platform activity",
        "Handle support requests",
        "Oversee content moderation"
      ]
    }
  },

  tokenCreation: {
    process: [
      "Navigate to token creation section",
      "Fill in token details (name, symbol, total supply)",
      "Set token price and liquidity amount",
      "Deploy contract to blockchain",
      "Add liquidity to Uniswap for trading"
    ],
    requirements: [
      "Must be an artist (complete artist profile)",
      "Need MetaMask or compatible wallet",
      "Sufficient ETH for gas fees (testnet)",
      "Token name and symbol (unique)",
      "Total supply amount",
      "Initial liquidity amount"
    ],
    tips: [
      "Choose a memorable token name and symbol",
      "Consider your total supply carefully",
      "Start with reasonable liquidity amount",
      "Test on testnet before mainnet",
      "Promote your token to build community"
    ]
  },

  contentManagement: {
    hierarchy: "Libraries > Albums > Tracks",
    libraries: {
      description: "Top-level content containers for organizing your music",
      requirements: ["Must have created a token first", "Artist role required"],
      tips: ["Use descriptive names", "Organize by theme or genre"]
    },
    albums: {
      description: "Collections of tracks within a library",
      features: ["Thumbnail images", "Track ordering", "Release dates"],
      tips: ["Add attractive thumbnails", "Organize tracks logically"]
    },
    tracks: {
      description: "Individual music files",
      supportedFormats: ["MP3", "WAV", "FLAC"],
      features: ["Thumbnails", "Lyrics", "Comments", "Likes"],
      tips: ["Use high-quality audio", "Add engaging thumbnails", "Encourage fan interaction"]
    }
  },

  blockchain: {
    network: "Base Sepolia (Testnet)",
    mainnetPlans: "Base Mainnet",
    wallet: "MetaMask recommended",
    gasToken: "ETH",
    dex: "Uniswap V3",
    contractStandards: "ERC-20 for tokens"
  },

  trading: {
    platform: "Uniswap V3",
    process: [
      "Artist creates token",
      "Artist adds liquidity (ETH + Token)",
      "Fans can buy/sell tokens",
      "Trading happens on Uniswap interface"
    ],
    liquidity: {
      description: "ETH paired with artist tokens to enable trading",
      importance: "Required for fans to buy/sell tokens",
      tips: ["Start with modest liquidity", "Can add more later", "Higher liquidity = less price volatility"]
    }
  },

  commonQuestions: {
    artists: [
      {
        q: "How do I create my first token?",
        a: "Complete your artist profile, then go to the token creation section. Fill in your token name, symbol, and total supply. Set your price and liquidity amount, then deploy!"
      },
      {
        q: "What is liquidity and why do I need it?",
        a: "Liquidity is ETH paired with your tokens that enables trading on Uniswap. Without liquidity, fans can't buy your tokens. Start with a small amount and add more as needed."
      },
      {
        q: "How much does it cost to create a token?",
        a: "On testnet, you only need test ETH for gas fees (free). On mainnet, you'll need real ETH for gas fees, typically $10-50 depending on network congestion."
      },
      {
        q: "How do I upload music?",
        a: "First create a library, then add albums to it, then upload tracks to your albums. Make sure you have a token created first!"
      },
      {
        q: "What file formats are supported?",
        a: "We support MP3, WAV, and FLAC audio formats. For images, we support JPG, PNG, and GIF."
      }
    ],
    fans: [
      {
        q: "How do I buy artist tokens?",
        a: "Connect your MetaMask wallet, find an artist you like, and click 'Buy Tokens'. You'll be redirected to Uniswap to complete the purchase with ETH."
      },
      {
        q: "What can I do with artist tokens?",
        a: "Artist tokens give you access to exclusive content, community features, and the ability to support your favorite artists. You can also trade them on Uniswap."
      },
      {
        q: "How do I set up MetaMask?",
        a: "Download MetaMask from metamask.io, create a wallet, and add the Base Sepolia testnet. Get test ETH from a faucet to start trading."
      },
      {
        q: "How do I access exclusive content?",
        a: "Once you own an artist's tokens, you'll automatically see their gated content when browsing their profile. The more tokens you hold, the more access you may have."
      }
    ],
    general: [
      {
        q: "What is TEMOC?",
        a: "TEMOC is a tokenized creator platform where artists can launch their own tokens and fans can support them by buying tokens to access exclusive content."
      },
      {
        q: "Is TEMOC free to use?",
        a: "Creating an account is free. Artists pay gas fees for token creation, and fans pay for tokens and gas fees for transactions."
      },
      {
        q: "What blockchain does TEMOC use?",
        a: "We currently use Base Sepolia testnet for testing, with plans to launch on Base mainnet."
      },
      {
        q: "How do I get help?",
        a: "You can use this chat assistant, check our help documentation, or contact <NAME_EMAIL>"
      }
    ]
  },

  troubleshooting: {
    wallet: [
      "Make sure MetaMask is installed and unlocked",
      "Check you're on the correct network (Base Sepolia)",
      "Ensure you have sufficient ETH for gas fees",
      "Try refreshing the page and reconnecting wallet"
    ],
    tokenCreation: [
      "Verify you've completed your artist profile",
      "Check token name/symbol aren't already taken",
      "Ensure sufficient ETH balance for gas",
      "Try reducing gas price if transaction fails"
    ],
    trading: [
      "Confirm token has liquidity on Uniswap",
      "Check slippage tolerance settings",
      "Verify sufficient ETH balance",
      "Try smaller trade amounts first"
    ]
  }
};

export const getContextualHelp = (userRole: string, currentPage: string) => {
  const roleSpecific = TEMOC_KNOWLEDGE_BASE.userRoles[userRole as keyof typeof TEMOC_KNOWLEDGE_BASE.userRoles];
  const pageSpecific = getPageSpecificHelp(currentPage);
  
  return {
    role: roleSpecific,
    page: pageSpecific,
    quickActions: getQuickActions(userRole, currentPage)
  };
};

const getPageSpecificHelp = (page: string) => {
  const pageHelp: Record<string, string[]> = {
    profile: [
      "Complete your profile information",
      "Upload a profile picture and cover photo",
      "Add social media links",
      "Set your bio and description"
    ],
    explore: [
      "Discover trending artists",
      "Listen to popular tracks",
      "Find new music to enjoy",
      "Follow artists you like"
    ],
    discovery: [
      "Browse all artists on the platform",
      "Filter by genre or popularity",
      "Find emerging talent",
      "Explore different music styles"
    ],
    library: [
      "Create new libraries for your content",
      "Organize your music by theme",
      "Add albums to your libraries",
      "Upload tracks to your albums"
    ],
    trading: [
      "View your token portfolio",
      "Trade artist tokens",
      "Check token prices",
      "Manage your investments"
    ]
  };
  
  return pageHelp[page] || ["Navigate the platform", "Explore available features"];
};

const getQuickActions = (userRole: string, currentPage: string) => {
  if (userRole === 'artist') {
    return [
      "How to create a token?",
      "How to upload music?",
      "How to add liquidity?",
      "How to promote my content?"
    ];
  } else {
    return [
      "How to buy tokens?",
      "How to discover artists?",
      "How to access content?",
      "How to use MetaMask?"
    ];
  }
};
