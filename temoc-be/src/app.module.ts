import { Module } from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { MongooseModule } from "@nestjs/mongoose";
import { UsersModule } from "./users/users.module";
import { AuthModule } from "./auth/auth.module";
import { ContentModule } from "./content/content.module";
import { TokensModule } from "./tokens/tokens.module";
import { TransactionModule } from "./transaction/transaction.module";
import { CloudinaryModule } from "./cloudinary/cloudinary.module";
import { DynamicModule } from "./dynamic/dynamic.module";
import { AdminModule } from "./admin/admin.module";
// import { LiquidityModule } from './liquidity/liquidity.module';
import { EmailModule } from './email/email.module';
import { LiquidityModule } from "./liquidity/liquidity.module";
import { PresaleModule } from "./presale/presale.module";
import { ChatModule } from "./chat/chat.module";

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        uri: configService.get<string>("MONGODB_URI"),
      }),
      inject: [ConfigService],
    }),
    UsersModule,
    AuthModule,
    CloudinaryModule,
    DynamicModule,
    TokensModule,
    TransactionModule,
    ContentModule,
    AdminModule,
    LiquidityModule,
    EmailModule,
    PresaleModule,
    ChatModule,
  ],
})
export class AppModule {}
