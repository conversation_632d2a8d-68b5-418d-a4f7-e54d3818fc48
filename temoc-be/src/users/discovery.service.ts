import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, Types } from "mongoose";
import { User, UserRole } from "./schemas/user.schema";
import { Follow } from "./schemas/follow.schema";
import { Token } from "../tokens/entities/token.entity";

export interface DiscoveryFilters {
  genre?: string[];
  isVerified?: boolean;
  hasToken?: boolean;
  location?: string;
  search?: string;
  sortBy?: "popular" | "newest" | "alphabetical" | "tokenPrice" | "mostActive";
  sortOrder?: "asc" | "desc";
}

@Injectable()
export class DiscoveryService {
  constructor(
    @InjectModel(User.name) private userModel: Model<User>,
    @InjectModel(Follow.name) private followModel: Model<Follow>,
    @InjectModel(Token.name) private tokenModel: Model<Token>
  ) {}

  async discoverArtists(
    filters: DiscoveryFilters,
    page: number = 1,
    limit: number = 20,
    currentUserId?: string
  ) {
    const skip = (page - 1) * limit;
    const query: any = { role: UserRole.ARTIST, isActive: true };

    // Apply filters
    if (filters.genre && filters.genre.length > 0) {
      query["artistProfile.genre"] = { $in: filters.genre };
    }

    if (filters.isVerified !== undefined) {
      query["artistProfile.isVerified"] = filters.isVerified;
    }

    if (filters.location) {
      query.location = new RegExp(filters.location, "i");
    }

    if (filters.genre && filters.genre.length > 0) {
      query["artistProfile.genre"] = { $in: filters.genre };
    }

    if (filters.search) {
      query.$or = [
        { username: new RegExp(filters.search, "i") },
        { displayName: new RegExp(filters.search, "i") },
        { firstName: new RegExp(filters.search, "i") },
        { lastName: new RegExp(filters.search, "i") },
        { "artistProfile.genre": new RegExp(filters.search, "i") },
      ];
    }

    // Build aggregation pipeline
    const pipeline: any[] = [
      { $match: query },
      {
        $lookup: {
          from: "follows",
          let: { userId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$following", "$$userId"] },
                    { $eq: ["$isActive", true] },
                  ],
                },
              },
            },
          ],
          as: "followerData",
        },
      },
      {
        $lookup: {
          from: "tokens",
          localField: "_id",
          foreignField: "user",
          as: "tokens",
        },
      },
      {
        $addFields: {
          followersCount: { $size: "$followerData" },
          hasToken: { $gt: [{ $size: "$tokens" }, 0] },
          tokenCount: { $size: "$tokens" },
        },
      },
    ];

    // Apply token filter
    if (filters.hasToken !== undefined) {
      pipeline.push({
        $match: { hasToken: filters.hasToken },
      });
    }

    // Apply sorting
    let sortStage: any = {};
    switch (filters.sortBy) {
      case "popular":
        sortStage = { followersCount: filters.sortOrder === "asc" ? 1 : -1 };
        break;
      case "newest":
        sortStage = { createdAt: filters.sortOrder === "asc" ? 1 : -1 };
        break;
      case "alphabetical":
        sortStage = { displayName: filters.sortOrder === "asc" ? 1 : -1 };
        break;
      case "mostActive":
        sortStage = { lastActiveAt: filters.sortOrder === "asc" ? 1 : -1 };
        break;
      default:
        sortStage = { followersCount: -1 }; // Default to popular
    }

    pipeline.push({ $sort: sortStage });
    pipeline.push({ $skip: skip });
    pipeline.push({ $limit: limit });

    // Project fields
    pipeline.push({
      $project: {
        username: 1,
        displayName: 1,
        firstName: 1,
        lastName: 1,
        avatarUrl: 1,
        coverPicture: 1,
        location: 1,
        lastActiveAt: 1,
        'artistProfile.genre': 1,
        'artistProfile.isVerified': 1,
        'artistProfile.bio': 1,
        'artistProfile.coverPhoto': 1,
        'artistProfile.profilePic': 1,
        followersCount: 1,
        hasToken: 1,
        tokenCount: 1,
        tokens: {
          $map: {
            input: "$tokens",
            as: "token",
            in: {
              name: "$$token.name",
              symbol: "$$token.symbol",
              address: "$$token.address",
            },
          },
        },
      },
    });

    // Execute the main pipeline
    const artists = await this.userModel.aggregate(pipeline);

    // Calculate total count with the same filters
    let totalCount: number;
    if (filters.hasToken !== undefined) {
      // If hasToken filter is applied, we need to count using aggregation
      const countPipeline = [
        { $match: query },
        {
          $lookup: {
            from: "tokens",
            localField: "_id",
            foreignField: "user",
            as: "tokens",
          },
        },
        {
          $addFields: {
            hasToken: { $gt: [{ $size: "$tokens" }, 0] },
          },
        },
        {
          $match: { hasToken: filters.hasToken },
        },
        { $count: "total" },
      ];

      const countResult = await this.userModel.aggregate(countPipeline);
      totalCount = countResult.length > 0 ? countResult[0].total : 0;
    } else {
      // For other filters, simple count is sufficient
      totalCount = await this.userModel.countDocuments(query);
    }

    // Add follow status for current user
    if (currentUserId && artists.length > 0) {
      const artistIds = artists.map((a) => a._id);
      const followRelations = await this.followModel.find({
        follower: new Types.ObjectId(currentUserId),
        following: { $in: artistIds },
        isActive: true,
      });

      const followingSet = new Set(
        followRelations.map((f) => f.following.toString())
      );

      artists.forEach((artist) => {
        artist.isFollowing = followingSet.has(artist._id.toString());
      });
    }

    return {
      artists,
      pagination: {
        page,
        limit,
        total: totalCount,
        pages: Math.ceil(totalCount / limit),
      },
    };
  }

  async getTrendingArtists(limit: number = 10) {
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    const pipeline = [
      {
        $match: {
          role: UserRole.ARTIST,
          isActive: true,
          lastActiveAt: { $gte: oneWeekAgo },
        },
      },
      {
        $lookup: {
          from: "follows",
          let: { userId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$following", "$$userId"] },
                    { $eq: ["$isActive", true] },
                    { $gte: ["$followedAt", oneWeekAgo] },
                  ],
                },
              },
            },
          ],
          as: "recentFollowers",
        },
      },
      {
        $addFields: {
          recentFollowersCount: { $size: "$recentFollowers" },
          activityScore: {
            $add: [
              { $size: "$recentFollowers" },
              { $cond: [{ $gte: ["$lastActiveAt", oneWeekAgo] }, 5, 0] },
            ],
          },
        },
      },
      { $sort: { activityScore: -1 as -1, lastActiveAt: -1 as -1 } },
      { $limit: limit },
      {
        $project: {
          username: 1,
          displayName: 1,
          avatarUrl: 1,
          coverPicture: 1,
          'artistProfile.genre': 1,
          'artistProfile.isVerified': 1,
          'artistProfile.bio': 1,
          'artistProfile.coverPhoto': 1,
          'artistProfile.profilePic': 1,
          // "artistProfile.genre": 1,
          // "artistProfile.isVerified": 1,
          recentFollowersCount: 1,
          activityScore: 1,
        },
      },
    ];

    return this.userModel.aggregate(pipeline);
  }

  async getNewVerifiedArtists(limit: number = 10) {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    return this.userModel
      .find({
        role: UserRole.ARTIST,
        isActive: true,
        "artistProfile.isVerified": true,
        "artistProfile.applicationReviewDate": { $gte: thirtyDaysAgo },
      })
      .select(
        "username displayName avatarUrl artistProfile.genre artistProfile.isVerified artistProfile.profilePic artistProfile.coverPhoto"
      )
      .sort({ "artistProfile.applicationReviewDate": -1 })
      .limit(limit);
  }

  async getPopularArtists(limit: number = 10, userId?: Types.ObjectId) {
    const pipeline: any[] = [
      {
        $match: {
          role: UserRole.ARTIST,
          isActive: true,
          ...(userId && { _id: { $ne: userId } }),
        },
      },
      {
        $lookup: {
          from: "follows",
          let: { userId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$following", "$$userId"] },
                    { $eq: ["$isActive", true] },
                  ],
                },
              },
            },
          ],
          as: "followers",
        },
      },
      {
        $addFields: {
          followersCount: { $size: "$followers" },
        },
      },
      { $sort: { followersCount: -1 } },
      { $limit: limit },
      {
        $project: {
          username: 1,
          displayName: 1,
          avatarUrl: 1,
          coverPicture: 1,
          'artistProfile.genre': 1,
          'artistProfile.isVerified': 1,
          'artistProfile.bio': 1,
          'artistProfile.coverPhoto': 1,
          'artistProfile.profilePic': 1,
          followersCount: 1
        }
      }
    ];

    return this.userModel.aggregate(pipeline);
  }

  async getRecommendedArtists(userId: string, limit: number = 10) {
    // Get user's following list
    const userFollowing = await this.followModel
      .find({
        follower: new Types.ObjectId(userId),
        isActive: true,
      })
      .select("following");

    const followingIds = userFollowing.map((f) => f.following);

    if (followingIds.length === 0) {
      // If user doesn't follow anyone, return popular artists
      return this.getPopularArtists(limit);
    }

    // Find artists followed by people the user follows
    const pipeline = [
      {
        $match: {
          follower: { $in: followingIds },
          following: { $nin: [...followingIds, new Types.ObjectId(userId)] },
          isActive: true,
        },
      },
      {
        $group: {
          _id: "$following",
          mutualConnections: { $sum: 1 },
        },
      },
      { $sort: { mutualConnections: -1 as -1 } },
      { $limit: limit },
      {
        $lookup: {
          from: "users",
          localField: "_id",
          foreignField: "_id",
          as: "artist",
        },
      },
      { $unwind: "$artist" },
      {
        $match: {
          "artist.role": UserRole.ARTIST,
          "artist.isActive": true,
        },
      },
      {
        $project: {
          username: '$artist.username',
          displayName: '$artist.displayName',
          avatarUrl: '$artist.avatarUrl',
          coverPicture: '$artist.coverPicture',
          'artistProfile.genre': '$artist.artistProfile.genre',
          'artistProfile.isVerified': '$artist.artistProfile.isVerified',
          'artistProfile.bio': '$artist.artistProfile.bio',
          'artistProfile.coverPhoto': '$artist.artistProfile.coverPhoto',
          'artistProfile.profilePic': '$artist.artistProfile.profilePic',
          mutualConnections: 1
        }
      }
    ];

    return this.followModel.aggregate(pipeline);
  }
}
